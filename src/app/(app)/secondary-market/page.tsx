'use client';

import { useEffect, useState } from 'react';

import { MarketplaceFilters } from '@/components/shared/marketplace-filters';
import { MarketplaceOrderList } from '@/components/shared/marketplace-order-list';
import { CachePatterns, type OrderEntity } from '@/constants/core.constants';
import { useAppCache } from '@/contexts/AppCacheContext';
import { useInfiniteScroll } from '@/hooks/use-infinite-scroll';
import { useMarketplaceFilters } from '@/hooks/use-marketplace-filters';
import { useRootContext } from '@/root-context';

import { SecondaryOrderDetailsDrawer } from './components/secondary-order-details-drawer';
import { useSecondaryMarketOrders } from './hooks/use-secondary-market-orders';

export default function SecondaryMarketPage() {
  const { collections, refetchUser, currentUser } = useRootContext();
  const cache = useAppCache();
  const [showOrderDetailsDrawer, setShowOrderDetailsDrawer] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<OrderEntity | null>(null);

  const filters = useMarketplaceFilters();
  const ordersFilters = {
    ...filters.getFilters(),
    currentUserId: currentUser?.id,
  };

  const { state, loadOrders, loadMoreOrders } = useSecondaryMarketOrders({
    filters: ordersFilters,
  });

  const loadMoreRef = useInfiniteScroll({
    hasMore: state.hasMore,
    loading: state.loading || state.loadingMore,
    onLoadMore: loadMoreOrders,
  });

  useEffect(() => {
    loadOrders(true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    filters.minPrice,
    filters.maxPrice,
    filters.selectedCollection,
    filters.sortBy,
  ]);

  const handleOrderClick = (order: OrderEntity) => {
    setSelectedOrder(order);
    setShowOrderDetailsDrawer(true);
  };

  const handleOrderAction = () => {
    cache.invalidatePattern(CachePatterns.ORDERS_FOR_BUYERS);
    cache.invalidatePattern(CachePatterns.ORDERS_FOR_SELLERS);
    cache.invalidatePattern(CachePatterns.SECONDARY_MARKET_ORDERS);
    loadOrders(true);
    refetchUser();
  };

  return (
    <div className="space-y-2 bg-[#17212b] min-h-screen">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-[#f5f5f5]">Secondary market</h1>
      </div>

      <MarketplaceFilters
        minPrice={filters.minPrice}
        maxPrice={filters.maxPrice}
        selectedCollection={filters.selectedCollection}
        sortBy={filters.sortBy}
        collections={collections}
        onMinPriceChange={filters.setMinPrice}
        onMaxPriceChange={filters.setMaxPrice}
        onCollectionChange={filters.setSelectedCollection}
        onSortChange={filters.setSortBy}
      />

      <MarketplaceOrderList
        variant="secondary-market"
        orders={state.orders}
        collections={collections}
        loading={state.loading}
        loadingMore={state.loadingMore}
        emptyMessage="No orders found in secondary market"
        onOrderClick={handleOrderClick}
        ref={loadMoreRef}
      />

      <SecondaryOrderDetailsDrawer
        open={showOrderDetailsDrawer}
        onOpenChange={setShowOrderDetailsDrawer}
        order={selectedOrder}
        onOrderAction={handleOrderAction}
      />
    </div>
  );
}
